#!/usr/bin/env python3
"""
Setup script for Fortinet Bypasser
"""

import os
import sys
import subprocess
import platform


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 6):
        print("❌ Python 3.6 or higher is required")
        return False
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    return True


def install_requirements():
    """Install required packages"""
    print("Installing requirements...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✓ Requirements installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install requirements: {e}")
        return False


def check_permissions():
    """Check if we have necessary permissions"""
    print("Checking permissions...")
    
    # Check if we can create sockets (needed for some bypass techniques)
    try:
        import socket
        test_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        test_socket.close()
        print("✓ Socket creation permissions OK")
    except Exception as e:
        print(f"⚠️  Socket permissions may be limited: {e}")
    
    # Check if we can resolve DNS
    try:
        import socket
        socket.gethostbyname('google.com')
        print("✓ DNS resolution OK")
    except Exception as e:
        print(f"⚠️  DNS resolution may be limited: {e}")
    
    return True


def create_sample_config():
    """Create sample configuration if it doesn't exist"""
    if not os.path.exists('config.json'):
        print("⚠️  config.json not found, but it should have been created")
        return False
    
    print("✓ Configuration file exists")
    return True


def run_basic_test():
    """Run a basic test to verify installation"""
    print("Running basic test...")
    
    try:
        # Import the main module
        from fortinet_bypasser import FortinetBypasser
        print("✓ Main module imports successfully")
        
        # Create a bypasser instance
        bypasser = FortinetBypasser("example.com", verbose=False)
        print("✓ Bypasser instance created successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Basic test failed: {e}")
        return False


def show_usage_examples():
    """Show usage examples"""
    print("\n" + "=" * 60)
    print("INSTALLATION COMPLETE!")
    print("=" * 60)
    
    print("\nUsage Examples:")
    print("-" * 20)
    
    examples = [
        ("Basic test", "python fortinet_bypasser.py google.com -v"),
        ("Specific technique", "python fortinet_bypasser.py example.com -t headers"),
        ("Custom port", "python fortinet_bypasser.py example.com -p 8080"),
        ("HTTP tunnel", "python proxy_tunnel.py --type http --local-port 8080 --remote-host example.com --remote-port 80"),
        ("Run tests", "python test_bypasser.py")
    ]
    
    for description, command in examples:
        print(f"\n{description}:")
        print(f"  {command}")
    
    print("\n" + "=" * 60)
    print("⚠️  REMEMBER: Use only on networks you own or have permission to test!")
    print("=" * 60)


def main():
    """Main setup function"""
    print("Fortinet Bypasser Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        return 1
    
    # Install requirements
    if not install_requirements():
        return 1
    
    # Check permissions
    check_permissions()
    
    # Verify config exists
    if not create_sample_config():
        return 1
    
    # Run basic test
    if not run_basic_test():
        print("⚠️  Basic test failed, but installation may still work")
    
    # Show usage examples
    show_usage_examples()
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
