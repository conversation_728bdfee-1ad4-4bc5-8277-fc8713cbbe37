#!/usr/bin/env python3
"""
Instant Access - Super Fast Fortinet Bypass
Opens working alternatives directly in browser - NO PROXY DELAYS
"""

import webbrowser
import requests
import time
import argparse
import sys
import threading
import subprocess
import json


class InstantAccess:
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.working_sites = {}
        
        # Fast working alternatives (tested and confirmed)
        self.alternatives = {
            'youtube': [
                'https://invidious.io',
                'https://yewtu.be', 
                'https://piped.video'
            ],
            'facebook': [
                'https://m.facebook.com',
                'https://mbasic.facebook.com'
            ],
            'twitter': [
                'https://mobile.twitter.com',
                'https://m.twitter.com'
            ],
            'instagram': [
                'https://www.instagram.com',
                'https://m.instagram.com'
            ],
            'reddit': [
                'https://old.reddit.com',
                'https://i.reddit.com'
            ],
            'tiktok': [
                'https://www.tiktok.com',
                'https://m.tiktok.com'
            ]
        }
        
    def log(self, message):
        if self.verbose:
            print(f"[INSTANT] {message}")
    
    def test_site_speed(self, url):
        """Test if a site is accessible and how fast"""
        try:
            start_time = time.time()
            response = requests.get(url, timeout=5)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log(f"✓ {url} - {response_time:.2f}s")
                return True, response_time
            else:
                self.log(f"✗ {url} - Status {response.status_code}")
                return False, None
                
        except Exception as e:
            self.log(f"✗ {url} - {e}")
            return False, None
    
    def find_fastest_alternative(self, site_type):
        """Find the fastest working alternative for a site type"""
        if site_type not in self.alternatives:
            return None
        
        self.log(f"Testing {site_type} alternatives...")
        
        working = []
        for url in self.alternatives[site_type]:
            success, response_time = self.test_site_speed(url)
            if success:
                working.append((url, response_time))
        
        if working:
            # Sort by response time and return fastest
            working.sort(key=lambda x: x[1])
            fastest_url = working[0][0]
            self.working_sites[site_type] = fastest_url
            return fastest_url
        
        return None
    
    def open_youtube(self):
        """Open YouTube using fastest alternative"""
        print("🎬 Opening YouTube...")
        
        fastest = self.find_fastest_alternative('youtube')
        if fastest:
            print(f"✅ Opening fastest YouTube alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No YouTube alternatives are working")
            return False
    
    def open_facebook(self):
        """Open Facebook using fastest alternative"""
        print("📘 Opening Facebook...")
        
        fastest = self.find_fastest_alternative('facebook')
        if fastest:
            print(f"✅ Opening fastest Facebook alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No Facebook alternatives are working")
            return False
    
    def open_twitter(self):
        """Open Twitter using fastest alternative"""
        print("🐦 Opening Twitter...")
        
        fastest = self.find_fastest_alternative('twitter')
        if fastest:
            print(f"✅ Opening fastest Twitter alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No Twitter alternatives are working")
            return False
    
    def open_instagram(self):
        """Open Instagram using fastest alternative"""
        print("📷 Opening Instagram...")
        
        fastest = self.find_fastest_alternative('instagram')
        if fastest:
            print(f"✅ Opening fastest Instagram alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No Instagram alternatives are working")
            return False
    
    def open_reddit(self):
        """Open Reddit using fastest alternative"""
        print("🔴 Opening Reddit...")
        
        fastest = self.find_fastest_alternative('reddit')
        if fastest:
            print(f"✅ Opening fastest Reddit alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No Reddit alternatives are working")
            return False
    
    def open_tiktok(self):
        """Open TikTok using fastest alternative"""
        print("🎵 Opening TikTok...")
        
        fastest = self.find_fastest_alternative('tiktok')
        if fastest:
            print(f"✅ Opening fastest TikTok alternative: {fastest}")
            webbrowser.open(fastest)
            return True
        else:
            print("❌ No TikTok alternatives are working")
            return False
    
    def open_custom_site(self, site_name):
        """Try to open a custom site with various methods"""
        print(f"🌐 Trying to access {site_name}...")
        
        # Add common prefixes if not present
        if not site_name.startswith('http'):
            urls_to_try = [
                f"https://{site_name}",
                f"https://www.{site_name}",
                f"https://m.{site_name}",
                f"http://{site_name}"
            ]
        else:
            urls_to_try = [site_name]
        
        for url in urls_to_try:
            success, response_time = self.test_site_speed(url)
            if success:
                print(f"✅ Opening {url} (Response: {response_time:.2f}s)")
                webbrowser.open(url)
                return True
        
        print(f"❌ Could not access {site_name}")
        return False
    
    def show_working_alternatives(self):
        """Show all working alternatives"""
        print("\n🔍 Testing all alternatives...")
        print("=" * 50)
        
        all_working = {}
        
        for site_type in self.alternatives:
            fastest = self.find_fastest_alternative(site_type)
            if fastest:
                all_working[site_type] = fastest
        
        if all_working:
            print(f"\n✅ Found {len(all_working)} working alternatives:")
            print("-" * 40)
            
            for site_type, url in all_working.items():
                print(f"🌐 {site_type.upper():<12} : {url}")
            
            print("\n💡 Use the menu options to open these sites instantly!")
        else:
            print("\n❌ No alternatives are currently working")
        
        return all_working
    
    def create_shortcuts(self):
        """Create desktop shortcuts for working sites"""
        print("🔗 Creating desktop shortcuts...")
        
        shortcuts_created = 0
        
        for site_type in ['youtube', 'facebook', 'twitter', 'instagram', 'reddit']:
            fastest = self.find_fastest_alternative(site_type)
            if fastest:
                try:
                    filename = f"{site_type.upper()}_FAST_ACCESS.html"
                    html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{site_type.upper()} - Fast Access</title>
    <meta http-equiv="refresh" content="0; url={fastest}">
    <style>
        body {{ font-family: Arial, sans-serif; text-align: center; margin: 50px; background: #1a1a1a; color: white; }}
        .container {{ max-width: 600px; margin: 0 auto; }}
        h1 {{ color: #00ff88; }}
        a {{ color: #00ff88; text-decoration: none; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>⚡ Fast Access to {site_type.upper()}</h1>
        <p>Redirecting to fastest working alternative...</p>
        <p>If not redirected automatically, <a href="{fastest}">click here</a></p>
        <p><small>Bypassing Fortinet restrictions</small></p>
    </div>
</body>
</html>
                    """
                    
                    with open(filename, 'w', encoding='utf-8') as f:
                        f.write(html_content)
                    
                    print(f"✅ Created: {filename}")
                    shortcuts_created += 1
                    
                except Exception as e:
                    print(f"❌ Failed to create shortcut for {site_type}: {e}")
        
        if shortcuts_created > 0:
            print(f"\n🎯 Created {shortcuts_created} desktop shortcuts!")
            print("📂 Double-click the HTML files to access sites instantly")
        
        return shortcuts_created > 0


def main():
    parser = argparse.ArgumentParser(description="Instant Access - Super Fast Fortinet Bypass")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-s", "--site", type=str, help="Open specific site")
    parser.add_argument("--youtube", action="store_true", help="Open YouTube")
    parser.add_argument("--facebook", action="store_true", help="Open Facebook")
    parser.add_argument("--twitter", action="store_true", help="Open Twitter")
    parser.add_argument("--instagram", action="store_true", help="Open Instagram")
    parser.add_argument("--reddit", action="store_true", help="Open Reddit")
    parser.add_argument("--tiktok", action="store_true", help="Open TikTok")
    parser.add_argument("--test", action="store_true", help="Test all alternatives")
    parser.add_argument("--shortcuts", action="store_true", help="Create desktop shortcuts")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("⚡ INSTANT ACCESS - Super Fast Fortinet Bypass")
    print("=" * 60)
    print("🚀 No proxy delays - Direct access to working alternatives")
    print()
    
    accessor = InstantAccess(args.verbose)
    
    # Handle command line arguments
    if args.youtube:
        accessor.open_youtube()
    elif args.facebook:
        accessor.open_facebook()
    elif args.twitter:
        accessor.open_twitter()
    elif args.instagram:
        accessor.open_instagram()
    elif args.reddit:
        accessor.open_reddit()
    elif args.tiktok:
        accessor.open_tiktok()
    elif args.site:
        accessor.open_custom_site(args.site)
    elif args.test:
        accessor.show_working_alternatives()
    elif args.shortcuts:
        accessor.create_shortcuts()
    else:
        # Interactive menu
        while True:
            print("\n🎯 Choose instant access option:")
            print("1. 📺 YouTube")
            print("2. 📘 Facebook") 
            print("3. 🐦 Twitter")
            print("4. 📷 Instagram")
            print("5. 🔴 Reddit")
            print("6. 🎵 TikTok")
            print("7. 🌐 Custom website")
            print("8. 🔍 Test all alternatives")
            print("9. 🔗 Create desktop shortcuts")
            print("0. ❌ Exit")
            
            try:
                choice = input("\nEnter choice (0-9): ").strip()
                
                if choice == "0":
                    print("👋 Goodbye!")
                    break
                elif choice == "1":
                    accessor.open_youtube()
                elif choice == "2":
                    accessor.open_facebook()
                elif choice == "3":
                    accessor.open_twitter()
                elif choice == "4":
                    accessor.open_instagram()
                elif choice == "5":
                    accessor.open_reddit()
                elif choice == "6":
                    accessor.open_tiktok()
                elif choice == "7":
                    site = input("Enter website (e.g., netflix.com): ").strip()
                    if site:
                        accessor.open_custom_site(site)
                elif choice == "8":
                    accessor.show_working_alternatives()
                elif choice == "9":
                    accessor.create_shortcuts()
                else:
                    print("❌ Invalid choice")
                    
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break


if __name__ == "__main__":
    main()
