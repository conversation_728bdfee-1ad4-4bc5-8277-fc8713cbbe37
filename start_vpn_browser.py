#!/usr/bin/env python3
"""
Quick Launcher for Fortinet VPN Browser
Simple script to start the VPN browser with optimal settings
"""

import subprocess
import sys
import time
import webbrowser
import socket


def check_port_available(port):
    """Check if a port is available"""
    try:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', port))
            return True
    except OSError:
        return False


def find_available_port(start_port=8080):
    """Find an available port starting from start_port"""
    for port in range(start_port, start_port + 100):
        if check_port_available(port):
            return port
    return None


def main():
    print("🚀 Starting Fortinet VPN Browser...")
    print("=" * 50)
    
    # Find available port
    port = find_available_port(8080)
    if not port:
        print("❌ No available ports found")
        return 1
    
    print(f"📍 Using port: {port}")
    print(f"🌐 VPN Browser will be available at: http://localhost:{port}")
    print("🛡️ This will bypass Fortinet restrictions for ANY website")
    print("⏳ Starting server...")
    
    try:
        # Start the VPN browser
        cmd = [sys.executable, "fortinet_vpn_browser.py", "-p", str(port)]
        
        print("🔄 Launching VPN browser server...")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 VPN Browser stopped by user")
    except Exception as e:
        print(f"❌ Error starting VPN browser: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
