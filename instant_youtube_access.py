#!/usr/bin/env python3
"""
Instant YouTube Access - Direct Alternative Frontend Access
Bypasses Fortinet by using fast alternative YouTube frontends
"""

import webbrowser
import requests
import time
import argparse
import sys
import threading
import subprocess
from urllib.parse import urlparse, parse_qs


class InstantYouTubeAccess:
    def __init__(self, verbose=False):
        self.verbose = verbose
        self.working_frontends = []
        
        # Fast alternative YouTube frontends
        self.frontends = [
            {
                'name': 'Invidious (yewtu.be)',
                'url': 'https://yewtu.be',
                'search': 'https://yewtu.be/search?q=',
                'watch': 'https://yewtu.be/watch?v=',
                'description': 'Privacy-focused YouTube frontend'
            },
            {
                'name': 'Piped (piped.video)',
                'url': 'https://piped.video',
                'search': 'https://piped.video/results?search_query=',
                'watch': 'https://piped.video/watch?v=',
                'description': 'Fast YouTube alternative'
            },
            {
                'name': 'Invidious (invidious.io)',
                'url': 'https://invidious.io',
                'search': 'https://invidious.io/search?q=',
                'watch': 'https://invidious.io/watch?v=',
                'description': 'Open source YouTube frontend'
            },
            {
                'name': 'FreeTube Web',
                'url': 'https://freetube.writeas.com',
                'search': 'https://freetube.writeas.com/search?q=',
                'watch': 'https://freetube.writeas.com/watch?v=',
                'description': 'Desktop YouTube client web version'
            },
            {
                'name': 'ViewTube',
                'url': 'https://viewtube.io',
                'search': 'https://viewtube.io/results?search_query=',
                'watch': 'https://viewtube.io/watch?v=',
                'description': 'Modern YouTube frontend'
            }
        ]
    
    def log(self, message):
        if self.verbose:
            print(f"[INFO] {message}")
    
    def test_frontend_speed(self, frontend):
        """Test the speed and availability of a frontend"""
        try:
            start_time = time.time()
            response = requests.get(frontend['url'], timeout=8)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                self.log(f"✓ {frontend['name']} - {response_time:.2f}s")
                return True, response_time
            else:
                self.log(f"✗ {frontend['name']} - Status {response.status_code}")
                return False, None
                
        except Exception as e:
            self.log(f"✗ {frontend['name']} - {e}")
            return False, None
    
    def find_fastest_frontends(self):
        """Find the fastest working frontends"""
        self.log("Testing YouTube alternative frontends...")
        
        working = []
        for frontend in self.frontends:
            success, response_time = self.test_frontend_speed(frontend)
            if success:
                working.append((frontend, response_time))
        
        # Sort by response time
        working.sort(key=lambda x: x[1])
        self.working_frontends = [item[0] for item in working]
        
        return self.working_frontends
    
    def open_youtube_homepage(self):
        """Open YouTube homepage using fastest frontend"""
        if not self.working_frontends:
            self.find_fastest_frontends()
        
        if self.working_frontends:
            fastest = self.working_frontends[0]
            print(f"🚀 Opening {fastest['name']}...")
            print(f"📝 {fastest['description']}")
            webbrowser.open(fastest['url'])
            return True
        else:
            print("❌ No working frontends found")
            return False
    
    def search_youtube(self, query):
        """Search YouTube using fastest frontend"""
        if not self.working_frontends:
            self.find_fastest_frontends()
        
        if self.working_frontends:
            fastest = self.working_frontends[0]
            search_url = f"{fastest['search']}{query}"
            print(f"🔍 Searching '{query}' on {fastest['name']}...")
            webbrowser.open(search_url)
            return True
        else:
            print("❌ No working frontends found")
            return False
    
    def watch_video(self, video_id):
        """Watch a specific video using fastest frontend"""
        if not self.working_frontends:
            self.find_fastest_frontends()
        
        if self.working_frontends:
            fastest = self.working_frontends[0]
            watch_url = f"{fastest['watch']}{video_id}"
            print(f"🎬 Opening video {video_id} on {fastest['name']}...")
            webbrowser.open(watch_url)
            return True
        else:
            print("❌ No working frontends found")
            return False
    
    def show_all_working_frontends(self):
        """Show all working frontends with their URLs"""
        if not self.working_frontends:
            self.find_fastest_frontends()
        
        if self.working_frontends:
            print("\n✅ Working YouTube Alternatives:")
            print("=" * 50)
            
            for i, frontend in enumerate(self.working_frontends, 1):
                print(f"{i}. {frontend['name']}")
                print(f"   🌐 URL: {frontend['url']}")
                print(f"   📝 {frontend['description']}")
                print()
            
            return True
        else:
            print("❌ No working frontends found")
            return False
    
    def create_desktop_shortcuts(self):
        """Create desktop shortcuts for working frontends"""
        if not self.working_frontends:
            self.find_fastest_frontends()
        
        shortcuts_created = 0
        for frontend in self.working_frontends[:3]:  # Top 3 fastest
            try:
                # Create a simple HTML file that redirects
                filename = f"YouTube_{frontend['name'].replace(' ', '_')}.html"
                html_content = f"""
<!DOCTYPE html>
<html>
<head>
    <title>{frontend['name']} - YouTube Alternative</title>
    <meta http-equiv="refresh" content="0; url={frontend['url']}">
</head>
<body>
    <h1>Redirecting to {frontend['name']}...</h1>
    <p>If you're not redirected automatically, <a href="{frontend['url']}">click here</a>.</p>
</body>
</html>
                """
                
                with open(filename, 'w') as f:
                    f.write(html_content)
                
                print(f"✓ Created shortcut: {filename}")
                shortcuts_created += 1
                
            except Exception as e:
                self.log(f"Failed to create shortcut for {frontend['name']}: {e}")
        
        if shortcuts_created > 0:
            print(f"\n🎯 Created {shortcuts_created} desktop shortcuts!")
            print("📂 Double-click the HTML files to access YouTube alternatives")
        
        return shortcuts_created > 0
    
    def run_comprehensive_test(self):
        """Run comprehensive test of all methods"""
        print("🔍 Testing all YouTube bypass methods...")
        
        # Test frontends
        working_frontends = self.find_fastest_frontends()
        
        # Test direct access methods
        direct_methods = self.test_direct_access_methods()
        
        results = {
            'alternative_frontends': len(working_frontends),
            'direct_methods': direct_methods
        }
        
        return results, working_frontends
    
    def test_direct_access_methods(self):
        """Test direct access methods"""
        self.log("Testing direct access methods...")
        
        methods = [
            ('YouTube Mobile', 'https://m.youtube.com'),
            ('YouTube Gaming', 'https://gaming.youtube.com'),
            ('YouTube Music', 'https://music.youtube.com'),
            ('YouTube TV', 'https://tv.youtube.com')
        ]
        
        working_methods = []
        for name, url in methods:
            try:
                response = requests.get(url, timeout=5)
                if response.status_code == 200:
                    working_methods.append((name, url))
                    self.log(f"✓ {name} accessible")
                else:
                    self.log(f"✗ {name} blocked")
            except Exception as e:
                self.log(f"✗ {name} failed: {e}")
        
        return working_methods


def main():
    parser = argparse.ArgumentParser(description="Instant YouTube Access via Alternative Frontends")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose output")
    parser.add_argument("-o", "--open", action="store_true", help="Open YouTube homepage")
    parser.add_argument("-s", "--search", type=str, help="Search for videos")
    parser.add_argument("-w", "--watch", type=str, help="Watch specific video by ID")
    parser.add_argument("-l", "--list", action="store_true", help="List all working frontends")
    parser.add_argument("-c", "--shortcuts", action="store_true", help="Create desktop shortcuts")
    parser.add_argument("-t", "--test", action="store_true", help="Test all methods")
    
    args = parser.parse_args()
    
    print("=" * 70)
    print("⚡ Instant YouTube Access - Alternative Frontend Bypasser")
    print("=" * 70)
    
    accessor = InstantYouTubeAccess(args.verbose)
    
    if args.test:
        results, working = accessor.run_comprehensive_test()
        
        print(f"\n📊 Test Results:")
        print(f"✅ Working alternative frontends: {results['alternative_frontends']}")
        print(f"✅ Working direct methods: {len(results['direct_methods'])}")
        
        if working:
            print(f"\n🚀 Fastest frontend: {working[0]['name']}")
            print(f"🌐 URL: {working[0]['url']}")
    
    elif args.open:
        accessor.open_youtube_homepage()
    
    elif args.search:
        accessor.search_youtube(args.search)
    
    elif args.watch:
        accessor.watch_video(args.watch)
    
    elif args.list:
        accessor.show_all_working_frontends()
    
    elif args.shortcuts:
        accessor.create_desktop_shortcuts()
    
    else:
        # Interactive mode
        print("🎯 Choose an option:")
        print("1. Open YouTube homepage")
        print("2. Search for videos")
        print("3. Watch specific video")
        print("4. List all working alternatives")
        print("5. Create desktop shortcuts")
        print("6. Test all methods")
        
        try:
            choice = input("\nEnter choice (1-6): ").strip()
            
            if choice == "1":
                accessor.open_youtube_homepage()
            elif choice == "2":
                query = input("Enter search query: ").strip()
                if query:
                    accessor.search_youtube(query)
            elif choice == "3":
                video_id = input("Enter video ID: ").strip()
                if video_id:
                    accessor.watch_video(video_id)
            elif choice == "4":
                accessor.show_all_working_frontends()
            elif choice == "5":
                accessor.create_desktop_shortcuts()
            elif choice == "6":
                results, working = accessor.run_comprehensive_test()
                print(f"\n📊 Found {results['alternative_frontends']} working alternatives!")
            else:
                print("Invalid choice")
                
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")


if __name__ == "__main__":
    main()
