# Quick Usage Guide

## 🚀 Quick Start

### 1. Test Basic Functionality
```bash
# Test all bypass techniques against a target
python fortinet_bypasser.py httpbin.org -v

# Test specific technique
python fortinet_bypasser.py httpbin.org -t headers -v
```

### 2. Available Bypass Techniques

| Technique | Command | Description |
|-----------|---------|-------------|
| Headers | `-t headers` | HTTP header manipulation |
| DNS | `-t dns` | DNS tunneling |
| Fragment | `-t fragment` | HTTP request fragmentation |
| SNI | `-t sni` | SSL SNI bypass |
| Fronting | `-t fronting` | Domain fronting |
| All | `-t all` (default) | Run all techniques |

### 3. Proxy Tunneling

```bash
# HTTP tunnel (local port 8080 -> remote server)
python proxy_tunnel.py --type http --local-port 8080 --remote-host example.com --remote-port 80

# SSL tunnel
python proxy_tunnel.py --type ssl --local-port 8443 --remote-host example.com --remote-port 443

# DNS tunneling
python proxy_tunnel.py --type dns --domain your-domain.com
```

### 4. Testing Your Setup

```bash
# Run comprehensive tests
python test_bypasser.py

# Quick functionality test
python fortinet_bypasser.py httpbin.org -t headers -v
```

## 📋 Common Use Cases

### Corporate Network Testing
```bash
# Test internal server accessibility
python fortinet_bypasser.py internal.company.com -p 8080 -v

# Test with specific technique
python fortinet_bypasser.py internal.company.com -t fragment -v
```

### Firewall Bypass Testing
```bash
# Test multiple techniques
python fortinet_bypasser.py blocked-site.com -v

# Create tunnel for persistent access
python proxy_tunnel.py --type http --local-port 9090 --remote-host blocked-site.com --remote-port 80
```

### Security Research
```bash
# Test DNS exfiltration
python proxy_tunnel.py --type dns --domain test-domain.com

# SSL SNI testing
python fortinet_bypasser.py target.com -t sni -v
```

## ⚙️ Configuration

Edit `config.json` to customize:
- Timeout values
- Custom headers
- DNS servers
- Proxy settings
- Target configurations

## 🔧 Troubleshooting

### Common Issues

1. **Connection timeouts**: Increase timeout in config.json
2. **DNS resolution errors**: Try different DNS servers
3. **Permission errors**: Run with appropriate privileges
4. **SSL errors**: Check certificate validation settings

### Debug Mode
Always use `-v` flag for verbose output to see detailed information.

## ⚠️ Important Notes

- **Legal Use Only**: Only use on networks you own or have explicit permission to test
- **Educational Purpose**: This tool is for learning and authorized testing
- **Responsible Disclosure**: Follow proper security research practices
- **Network Monitoring**: Be aware that your activities may be logged

## 📊 Expected Results

### Successful Bypass Indicators
- HTTP 200 responses
- Successful SSL handshakes
- DNS query responses
- Tunnel establishment

### Common Failure Reasons
- Network restrictions
- Firewall detection
- Rate limiting
- Invalid configurations

## 🎯 Next Steps

1. **Learn the Techniques**: Understand how each bypass method works
2. **Customize Configuration**: Adapt settings for your environment
3. **Extend Functionality**: Add new bypass techniques
4. **Integrate with Tools**: Use with other security testing tools
5. **Stay Updated**: Keep up with new bypass methods

---

**Remember**: Always use responsibly and ethically!
