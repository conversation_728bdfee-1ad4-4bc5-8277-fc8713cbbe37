{"default_settings": {"timeout": 10, "max_retries": 3, "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36", "verbose": false}, "bypass_techniques": {"http_headers": {"enabled": true, "custom_headers": {"X-Forwarded-For": "127.0.0.1", "X-Real-IP": "127.0.0.1", "X-Originating-IP": "127.0.0.1", "X-Remote-IP": "127.0.0.1", "X-Remote-Addr": "127.0.0.1", "X-Client-IP": "127.0.0.1"}}, "dns_tunneling": {"enabled": true, "dns_servers": ["*******", "*******", "**************"], "record_types": ["A", "TXT", "CNAME", "MX"]}, "http_fragmentation": {"enabled": true, "fragment_delay": 0.1, "fragment_size": 64}, "ssl_sni": {"enabled": true, "sni_domains": ["google.com", "cloudflare.com", "amazon.com", "microsoft.com"]}, "domain_fronting": {"enabled": true, "front_domains": ["cloudfront.net", "amazonaws.com", "cloudflare.com", "fastly.com", "akamai.net", "azureedge.net"]}}, "proxy_settings": {"http_proxy": null, "https_proxy": null, "socks_proxy": null, "proxy_auth": null}, "tunneling": {"http_tunnel": {"local_port": 8080, "buffer_size": 4096}, "dns_tunnel": {"chunk_size": 50, "encoding": "base32"}, "ssl_tunnel": {"local_port": 8443, "verify_ssl": false}}, "targets": {"common_ports": [80, 443, 8080, 8443, 3128, 1080], "test_urls": ["http://httpbin.org/ip", "https://ifconfig.me", "http://checkip.amazonaws.com"]}}