#!/usr/bin/env python3
"""
Fast VPN Browser - High Performance Fortinet Bypasser
Optimized for speed with direct bypass methods
"""

import socket
import ssl
import threading
import time
import requests
import subprocess
import sys
import argparse
import json
import re
from urllib.parse import urlparse, urljoin, quote, unquote
import http.server
import socketserver
import webbrowser
import os


class FastVPNBrowser:
    def __init__(self, port=8080, verbose=False):
        self.port = port
        self.verbose = verbose
        self.running = False
        self.fast_dns_servers = ["*******", "*******", "*******"]
        self.working_ips = {}
        
    def log(self, message):
        if self.verbose:
            print(f"[FAST-VPN] {message}")
    
    def resolve_fast_ip(self, domain):
        """Quickly resolve domain to IP using fast DNS"""
        if domain in self.working_ips:
            return self.working_ips[domain]
        
        try:
            # Try direct resolution first (fastest)
            ip = socket.gethostbyname(domain)
            self.working_ips[domain] = ip
            self.log(f"✓ Fast resolve {domain} -> {ip}")
            return ip
        except:
            # Try alternative DNS servers
            for dns in self.fast_dns_servers:
                try:
                    result = subprocess.run([
                        'nslookup', domain, dns
                    ], capture_output=True, text=True, timeout=3)
                    
                    if result.returncode == 0:
                        lines = result.stdout.split('\n')
                        for line in lines:
                            if 'Address:' in line and not line.startswith('Server:'):
                                ip = line.split('Address:')[1].strip()
                                if ip and '.' in ip and not ip.startswith('127'):
                                    self.working_ips[domain] = ip
                                    self.log(f"✓ DNS resolve {domain} -> {ip}")
                                    return ip
                except:
                    continue
        
        return None
    
    def fetch_direct_fast(self, url):
        """Fetch content using fastest direct method"""
        try:
            parsed = urlparse(url)
            domain = parsed.netloc
            
            # Method 1: Direct IP access (fastest)
            ip = self.resolve_fast_ip(domain)
            if ip:
                try:
                    # Replace domain with IP but keep Host header
                    ip_url = url.replace(domain, ip)
                    headers = {
                        'Host': domain,
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Connection': 'keep-alive',
                        'Cache-Control': 'no-cache'
                    }
                    
                    response = requests.get(ip_url, headers=headers, timeout=8, verify=False)
                    if response.status_code == 200:
                        self.log(f"✓ Direct IP success for {url}")
                        return response.text
                except Exception as e:
                    self.log(f"Direct IP failed: {e}")
            
            # Method 2: Header manipulation (fast)
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'X-Forwarded-For': '*******',
                'X-Real-IP': '*******',
                'X-Originating-IP': '*******',
                'Via': '1.1 google',
                'Connection': 'keep-alive'
            }
            
            response = requests.get(url, headers=headers, timeout=8)
            if response.status_code == 200:
                self.log(f"✓ Header manipulation success for {url}")
                return response.text
                
        except Exception as e:
            self.log(f"Fast fetch failed for {url}: {e}")
        
        return None
    
    def fetch_alternative_frontend(self, url):
        """Use fast alternative frontends for popular sites"""
        parsed = urlparse(url)
        domain = parsed.netloc.lower()
        
        # YouTube alternatives (very fast)
        if 'youtube.com' in domain or 'youtu.be' in domain:
            alternatives = [
                "https://invidious.io",
                "https://yewtu.be",
                "https://piped.video"
            ]
            
            for alt in alternatives:
                try:
                    response = requests.get(alt, timeout=5)
                    if response.status_code == 200:
                        self.log(f"✓ YouTube alternative working: {alt}")
                        # Return a redirect page
                        return f"""
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>YouTube Access</title>
                            <meta http-equiv="refresh" content="0; url={alt}">
                        </head>
                        <body>
                            <h1>Redirecting to YouTube alternative...</h1>
                            <p>If not redirected, <a href="{alt}">click here</a></p>
                        </body>
                        </html>
                        """
                except:
                    continue
        
        # Facebook alternatives
        if 'facebook.com' in domain:
            try:
                # Try mobile version (often less restricted)
                mobile_url = url.replace('facebook.com', 'm.facebook.com')
                response = requests.get(mobile_url, timeout=5)
                if response.status_code == 200:
                    return response.text
            except:
                pass
        
        return None
    
    def create_fast_proxy_server(self):
        """Create optimized fast proxy server"""
        self.log(f"Starting Fast VPN Browser on port {self.port}...")
        
        class FastProxyHandler(http.server.BaseHTTPRequestHandler):
            def __init__(self, *args, vpn_browser=None, **kwargs):
                self.vpn_browser = vpn_browser
                super().__init__(*args, **kwargs)
            
            def do_GET(self):
                try:
                    if self.path.startswith('/browse/'):
                        target_url = unquote(self.path[8:])
                        if not target_url.startswith('http'):
                            target_url = 'https://' + target_url
                    elif self.path == '/' or self.path == '/browse':
                        self.send_fast_homepage()
                        return
                    else:
                        target_url = 'https://' + self.path[1:]
                    
                    # Try fast methods first
                    content = None
                    
                    # Method 1: Alternative frontends (fastest for popular sites)
                    content = self.vpn_browser.fetch_alternative_frontend(target_url)
                    
                    # Method 2: Direct fast fetch
                    if not content:
                        content = self.vpn_browser.fetch_direct_fast(target_url)
                    
                    if content:
                        self.send_response(200)
                        self.send_header('Content-type', 'text/html; charset=utf-8')
                        self.send_header('Access-Control-Allow-Origin', '*')
                        self.send_header('Cache-Control', 'no-cache')
                        self.end_headers()
                        
                        # Add fast navigation
                        enhanced_content = self.add_fast_navigation(content, target_url)
                        self.wfile.write(enhanced_content.encode('utf-8', errors='ignore'))
                    else:
                        self.send_fast_error_page(target_url)
                        
                except Exception as e:
                    self.send_response(500)
                    self.end_headers()
                    self.wfile.write(f"Fast VPN Error: {e}".encode())
            
            def send_fast_homepage(self):
                """Send optimized homepage"""
                html = """
                <!DOCTYPE html>
                <html>
                <head>
                    <title>⚡ Fast VPN Browser</title>
                    <style>
                        body { font-family: Arial, sans-serif; margin: 20px; background: #1a1a1a; color: white; }
                        .container { max-width: 900px; margin: 0 auto; }
                        .header { text-align: center; margin-bottom: 30px; }
                        .header h1 { color: #00ff88; margin-bottom: 10px; font-size: 2.5em; }
                        .header p { color: #888; font-size: 1.2em; }
                        .url-form { margin: 20px 0; text-align: center; }
                        .url-input { width: 60%; padding: 15px; border: 2px solid #00ff88; border-radius: 8px; font-size: 18px; background: #2a2a2a; color: white; }
                        .browse-btn { width: 20%; padding: 15px; background: #00ff88; color: black; border: none; border-radius: 8px; font-size: 18px; cursor: pointer; margin-left: 10px; font-weight: bold; }
                        .browse-btn:hover { background: #00cc66; }
                        .quick-links { margin: 30px 0; }
                        .quick-links h3 { color: #00ff88; text-align: center; font-size: 1.5em; }
                        .links-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
                        .quick-link { display: block; padding: 15px; background: #2a2a2a; color: #00ff88; text-decoration: none; border-radius: 8px; text-align: center; border: 2px solid #333; transition: all 0.3s; }
                        .quick-link:hover { background: #00ff88; color: black; transform: scale(1.05); }
                        .speed-info { background: #2a2a2a; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #00ff88; }
                        .speed-info h3 { color: #00ff88; margin-top: 0; }
                    </style>
                </head>
                <body>
                    <div class="container">
                        <div class="header">
                            <h1>⚡ Fast VPN Browser</h1>
                            <p>Lightning-fast bypass for Fortinet firewalls</p>
                        </div>
                        
                        <div class="url-form">
                            <form onsubmit="browseUrl(); return false;">
                                <input type="text" id="urlInput" class="url-input" placeholder="Enter website URL (e.g., youtube.com)" />
                                <button type="submit" class="browse-btn">⚡ GO</button>
                            </form>
                        </div>
                        
                        <div class="quick-links">
                            <h3>🚀 Instant Access</h3>
                            <div class="links-grid">
                                <a href="/browse/youtube.com" class="quick-link">📺 YouTube</a>
                                <a href="/browse/facebook.com" class="quick-link">📘 Facebook</a>
                                <a href="/browse/twitter.com" class="quick-link">🐦 Twitter</a>
                                <a href="/browse/instagram.com" class="quick-link">📷 Instagram</a>
                                <a href="/browse/reddit.com" class="quick-link">🔴 Reddit</a>
                                <a href="/browse/tiktok.com" class="quick-link">🎵 TikTok</a>
                                <a href="/browse/netflix.com" class="quick-link">🎬 Netflix</a>
                                <a href="/browse/twitch.tv" class="quick-link">🎮 Twitch</a>
                            </div>
                        </div>
                        
                        <div class="speed-info">
                            <h3>⚡ Speed Optimizations</h3>
                            <ul>
                                <li>🚀 Direct IP resolution for fastest access</li>
                                <li>⚡ Alternative frontends for popular sites</li>
                                <li>🎯 Smart header manipulation</li>
                                <li>💨 Minimal proxy overhead</li>
                                <li>🔄 Automatic fastest method selection</li>
                            </ul>
                        </div>
                    </div>
                    
                    <script>
                        function browseUrl() {
                            var url = document.getElementById('urlInput').value;
                            if (url) {
                                if (!url.startsWith('http://') && !url.startsWith('https://')) {
                                    url = 'https://' + url;
                                }
                                window.location.href = '/browse/' + encodeURIComponent(url);
                            }
                        }
                    </script>
                </body>
                </html>
                """
                
                self.send_response(200)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode())
            
            def add_fast_navigation(self, content, url):
                """Add minimal fast navigation"""
                nav_bar = f"""
                <div style="position: fixed; top: 0; left: 0; right: 0; background: #1a1a1a; color: #00ff88; padding: 8px; z-index: 9999; font-family: Arial, sans-serif; border-bottom: 2px solid #00ff88;">
                    <div style="max-width: 1200px; margin: 0 auto; display: flex; align-items: center; font-size: 14px;">
                        <span style="margin-right: 15px;">⚡ Fast VPN</span>
                        <span style="margin-right: 15px; color: white; max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">📍 {url}</span>
                        <a href="/" style="color: #00ff88; text-decoration: none; margin-right: 10px;">🏠</a>
                        <a href="javascript:history.back()" style="color: #00ff88; text-decoration: none; margin-right: 10px;">⬅️</a>
                        <input type="text" id="fastUrlInput" placeholder="New URL..." style="padding: 4px; margin-right: 8px; border: 1px solid #00ff88; border-radius: 3px; background: #2a2a2a; color: white; font-size: 12px;" />
                        <button onclick="fastBrowse()" style="padding: 4px 8px; background: #00ff88; color: black; border: none; border-radius: 3px; cursor: pointer; font-size: 12px;">GO</button>
                    </div>
                </div>
                <div style="height: 40px;"></div>
                <script>
                    function fastBrowse() {{
                        var url = document.getElementById('fastUrlInput').value;
                        if (url) {{
                            if (!url.startsWith('http://') && !url.startsWith('https://')) {{
                                url = 'https://' + url;
                            }}
                            window.location.href = '/browse/' + encodeURIComponent(url);
                        }}
                    }}
                </script>
                """
                
                if '<body' in content:
                    content = content.replace('<body', nav_bar + '<body', 1)
                else:
                    content = nav_bar + content
                
                return content
            
            def send_fast_error_page(self, url):
                """Send fast error page with alternatives"""
                html = f"""
                <!DOCTYPE html>
                <html>
                <head>
                    <title>⚡ Fast VPN - Quick Alternatives</title>
                    <style>
                        body {{ font-family: Arial, sans-serif; margin: 40px; background: #1a1a1a; color: white; text-align: center; }}
                        .error-container {{ max-width: 600px; margin: 0 auto; }}
                        .error-title {{ color: #ff6b6b; font-size: 24px; margin-bottom: 20px; }}
                        .alternatives {{ margin: 30px 0; }}
                        .alt-link {{ display: inline-block; margin: 10px; padding: 12px 20px; background: #00ff88; color: black; text-decoration: none; border-radius: 5px; font-weight: bold; }}
                        .alt-link:hover {{ background: #00cc66; }}
                    </style>
                </head>
                <body>
                    <div class="error-container">
                        <div class="error-title">⚡ Trying Faster Alternatives</div>
                        <p>Direct access failed for: <strong>{url}</strong></p>
                        <p>Try these instant alternatives:</p>
                        
                        <div class="alternatives">
                            <a href="https://invidious.io" class="alt-link" target="_blank">📺 YouTube Alternative</a>
                            <a href="https://yewtu.be" class="alt-link" target="_blank">🎬 Video Alternative</a>
                            <a href="/" class="alt-link">🏠 Back to VPN</a>
                        </div>
                        
                        <p style="color: #888; margin-top: 30px;">
                            💡 Tip: Try the alternative links above for instant access to popular sites
                        </p>
                    </div>
                </body>
                </html>
                """
                
                self.send_response(503)
                self.send_header('Content-type', 'text/html; charset=utf-8')
                self.end_headers()
                self.wfile.write(html.encode())
            
            def log_message(self, format, *args):
                pass
        
        def handler_factory(*args, **kwargs):
            return FastProxyHandler(*args, vpn_browser=self, **kwargs)
        
        try:
            with socketserver.TCPServer(("", self.port), handler_factory) as httpd:
                self.running = True
                self.log(f"✓ Fast VPN Browser started on http://localhost:{self.port}")
                
                print("=" * 70)
                print("⚡ FAST VPN Browser - OPTIMIZED FOR SPEED!")
                print("=" * 70)
                print(f"🚀 Access at: http://localhost:{self.port}")
                print("⚡ Optimized for maximum speed and minimal delays")
                print("🎯 Direct IP resolution + Alternative frontends")
                print("⏹️  Press Ctrl+C to stop")
                print("=" * 70)
                
                webbrowser.open(f"http://localhost:{self.port}")
                httpd.serve_forever()
                
        except KeyboardInterrupt:
            self.log("Fast VPN Browser stopped by user")
            self.running = False
        except Exception as e:
            self.log(f"Fast VPN Browser failed: {e}")
            return False
        
        return True


def main():
    parser = argparse.ArgumentParser(description="Fast VPN Browser for Fortinet")
    parser.add_argument("-p", "--port", type=int, default=8080, help="Port (default: 8080)")
    parser.add_argument("-v", "--verbose", action="store_true", help="Verbose logging")
    
    args = parser.parse_args()
    
    print("⚡ Starting FAST VPN Browser...")
    
    vpn_browser = FastVPNBrowser(args.port, args.verbose)
    vpn_browser.create_fast_proxy_server()


if __name__ == "__main__":
    main()
