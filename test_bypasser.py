#!/usr/bin/env python3
"""
Test script for Fortinet Bypasser
Tests the functionality of bypass techniques
"""

import sys
import time
from fortinet_bypasser import FortinetBypasser


def test_basic_functionality():
    """Test basic functionality with a known good target"""
    print("Testing basic functionality...")
    
    # Test with a reliable target
    test_targets = [
        ("httpbin.org", 80),
        ("google.com", 80),
        ("cloudflare.com", 80)
    ]
    
    for host, port in test_targets:
        print(f"\nTesting {host}:{port}")
        try:
            bypasser = FortinetBypasser(host, port, verbose=True)
            
            # Test individual techniques
            print("Testing HTTP header bypass...")
            header_result = bypasser.http_header_bypass()
            print(f"Header bypass: {'SUCCESS' if header_result else 'FAILED'}")
            
            print("Testing DNS tunneling...")
            dns_result = bypasser.dns_tunneling_bypass()
            print(f"DNS tunneling: {'SUCCESS' if dns_result else 'FAILED'}")
            
            print("Testing HTTP fragmentation...")
            frag_result = bypasser.http_fragmentation_bypass()
            print(f"HTTP fragmentation: {'SUCCESS' if frag_result else 'FAILED'}")
            
            # If any technique works, we consider the target reachable
            if any([header_result, dns_result, frag_result]):
                print(f"✓ {host} is reachable via bypass techniques")
                return True
            else:
                print(f"✗ {host} not reachable")
                
        except Exception as e:
            print(f"Error testing {host}: {e}")
            continue
    
    return False


def test_all_techniques():
    """Test all techniques against a target"""
    print("\nTesting all techniques...")
    
    # Use a reliable target for comprehensive testing
    target = "httpbin.org"
    
    try:
        bypasser = FortinetBypasser(target, 80, verbose=True)
        results = bypasser.run_all_bypasses()
        
        print("\nComprehensive Test Results:")
        print("=" * 50)
        
        success_count = 0
        for technique, success in results.items():
            status = "✓ SUCCESS" if success else "✗ FAILED"
            print(f"{technique:<25} : {status}")
            if success:
                success_count += 1
        
        print("=" * 50)
        print(f"Successful techniques: {success_count}/{len(results)}")
        
        return success_count > 0
        
    except Exception as e:
        print(f"Error in comprehensive test: {e}")
        return False


def test_configuration():
    """Test configuration loading"""
    print("\nTesting configuration...")
    
    try:
        import json
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        print("✓ Configuration file loaded successfully")
        
        # Check required sections
        required_sections = [
            'default_settings',
            'bypass_techniques',
            'proxy_settings',
            'tunneling',
            'targets'
        ]
        
        for section in required_sections:
            if section in config:
                print(f"✓ {section} section found")
            else:
                print(f"✗ {section} section missing")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration test failed: {e}")
        return False


def test_proxy_tunnel():
    """Test proxy tunnel functionality"""
    print("\nTesting proxy tunnel imports...")
    
    try:
        from proxy_tunnel import HTTPTunnel, DNSTunnel, SSLTunnel
        print("✓ Proxy tunnel modules imported successfully")
        
        # Test tunnel creation (without starting)
        http_tunnel = HTTPTunnel(8080, "example.com", 80)
        print("✓ HTTP tunnel object created")
        
        dns_tunnel = DNSTunnel("example.com")
        print("✓ DNS tunnel object created")
        
        ssl_tunnel = SSLTunnel(8443, "example.com", 443)
        print("✓ SSL tunnel object created")
        
        return True
        
    except Exception as e:
        print(f"✗ Proxy tunnel test failed: {e}")
        return False


def main():
    """Main test function"""
    print("Fortinet Bypasser Test Suite")
    print("=" * 40)
    
    tests = [
        ("Configuration Test", test_configuration),
        ("Proxy Tunnel Test", test_proxy_tunnel),
        ("Basic Functionality Test", test_basic_functionality),
        ("All Techniques Test", test_all_techniques)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{test_name}")
        print("-" * len(test_name))
        
        try:
            if test_func():
                print(f"✓ {test_name} PASSED")
                passed += 1
            else:
                print(f"✗ {test_name} FAILED")
        except Exception as e:
            print(f"✗ {test_name} ERROR: {e}")
    
    print("\n" + "=" * 40)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("❌ Some tests failed")
        return 1


if __name__ == "__main__":
    sys.exit(main())
