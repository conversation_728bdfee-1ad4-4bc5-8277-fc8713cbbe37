# Fortinet Bypasser

A comprehensive Python tool for testing and bypassing Fortinet firewall restrictions using various network security techniques.

## ⚠️ Legal Disclaimer

**FOR EDUCATIONAL AND AUTHORIZED TESTING PURPOSES ONLY**

This tool is designed for:
- Security researchers and penetration testers
- Network administrators testing their own systems
- Educational purposes in controlled environments

**DO NOT USE** on networks you don't own or without explicit written permission. Unauthorized use may violate laws and regulations.

## Features

### Bypass Techniques
- **HTTP Header Manipulation**: Bypass using custom headers and IP spoofing
- **DNS Tunneling**: Exfiltrate data and bypass restrictions via DNS queries
- **HTTP Fragmentation**: Split requests to evade deep packet inspection
- **SSL SNI Bypass**: Manipulate Server Name Indication for SSL connections
- **Domain Fronting**: Use CDN domains to mask true destinations

### Tunneling Capabilities
- **HTTP Tunneling**: Create HTTP-based tunnels through proxies
- **DNS Tunneling**: Tunnel data through DNS queries
- **WebSocket Tunneling**: Bypass HTTP-only restrictions
- **SSL Tunneling**: Encrypted tunneling for secure bypass

## Installation

1. Clone or download the repository
2. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### Basic Bypass Testing

Test all bypass techniques against a target:
```bash
python fortinet_bypasser.py example.com -v
```

Test specific technique:
```bash
python fortinet_bypasser.py example.com -t headers -v
```

Test with custom port:
```bash
python fortinet_bypasser.py example.com -p 8080 -v
```

### Available Techniques

- `headers`: HTTP header manipulation
- `dns`: DNS tunneling
- `fragment`: HTTP request fragmentation
- `sni`: SSL SNI bypass
- `fronting`: Domain fronting
- `all`: Run all techniques (default)

### Proxy Tunneling

Create HTTP tunnel:
```bash
python proxy_tunnel.py --type http --local-port 8080 --remote-host example.com --remote-port 80
```

Create SSL tunnel:
```bash
python proxy_tunnel.py --type ssl --local-port 8443 --remote-host example.com --remote-port 443
```

DNS tunneling:
```bash
python proxy_tunnel.py --type dns --domain example.com
```

HTTP tunnel through proxy:
```bash
python proxy_tunnel.py --type http --local-port 8080 --remote-host example.com --remote-port 80 --proxy-host proxy.example.com --proxy-port 3128
```

## Configuration

Edit `config.json` to customize:
- Default settings (timeouts, retries, user agents)
- Bypass technique parameters
- Proxy settings
- Target configurations

## Examples

### Example 1: Basic Firewall Test
```bash
# Test if a website is accessible through various bypass methods
python fortinet_bypasser.py google.com -v
```

### Example 2: Corporate Network Testing
```bash
# Test internal server accessibility
python fortinet_bypasser.py internal.company.com -p 8080 -t headers
```

### Example 3: Create Local Tunnel
```bash
# Create a local tunnel to bypass restrictions
python proxy_tunnel.py --type http --local-port 9090 --remote-host blocked-site.com --remote-port 80
# Then access via http://localhost:9090
```

### Example 4: DNS Data Exfiltration Test
```bash
# Test DNS tunneling capability
python proxy_tunnel.py --type dns --domain your-domain.com
```

## How It Works

### HTTP Header Bypass
Manipulates HTTP headers to appear as if requests are coming from trusted sources or internal networks.

### DNS Tunneling
Encodes data in DNS queries to bypass firewalls that allow DNS but block other protocols.

### HTTP Fragmentation
Splits HTTP requests into small fragments with delays to evade signature-based detection.

### SSL SNI Bypass
Uses different Server Name Indication values to bypass SNI-based filtering.

### Domain Fronting
Leverages CDN infrastructure to mask the true destination of requests.

## Advanced Usage

### Custom Headers
Modify the `config.json` file to add custom headers for specific bypass scenarios.

### Multiple Targets
Create scripts to test multiple targets:
```python
from fortinet_bypasser import FortinetBypasser

targets = ['site1.com', 'site2.com', 'site3.com']
for target in targets:
    bypasser = FortinetBypasser(target, verbose=True)
    results = bypasser.run_all_bypasses()
    print(f"Results for {target}: {results}")
```

### Integration with Other Tools
The bypasser can be integrated with other security testing tools:
```python
import fortinet_bypasser

# Use in your own scripts
bypasser = fortinet_bypasser.FortinetBypasser('target.com')
if bypasser.http_header_bypass():
    print("Header bypass successful - proceed with further testing")
```

## Troubleshooting

### Common Issues

1. **DNS Resolution Errors**: Check your DNS settings and try different DNS servers
2. **SSL Certificate Errors**: Use the `-k` flag to ignore SSL certificate validation
3. **Timeout Errors**: Increase timeout values in config.json
4. **Permission Denied**: Run with appropriate privileges for low-level network operations

### Debug Mode
Use verbose mode (`-v`) to see detailed information about each bypass attempt.

## Contributing

1. Fork the repository
2. Create a feature branch
3. Add your bypass technique or improvement
4. Test thoroughly
5. Submit a pull request

## Security Considerations

- Always test in isolated environments first
- Monitor network traffic during testing
- Be aware of logging and detection systems
- Follow responsible disclosure practices
- Keep tools updated with latest bypass techniques

## License

This project is for educational purposes only. Users are responsible for complying with all applicable laws and regulations.

## Changelog

### v1.0.0
- Initial release with basic bypass techniques
- HTTP header manipulation
- DNS tunneling capabilities
- SSL SNI bypass
- Domain fronting support
- Proxy tunneling modules

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the configuration options
3. Test in a controlled environment
4. Document your findings for improvement

---

**Remember**: Use responsibly and only on systems you own or have explicit permission to test.
